Stack trace:
Frame         Function      Args
0007FFFFB750  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA650) msys-2.0.dll+0x1FE8E
0007FFFFB750  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA28) msys-2.0.dll+0x67F9
0007FFFFB750  000210046832 (000210286019, 0007FFFFB608, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB750  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB750  000210068E24 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA30  00021006A225 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF81B400000 ntdll.dll
7FF819280000 KERNEL32.DLL
7FF818BE0000 KERNELBASE.dll
7FF819CE0000 USER32.dll
7FF8188D0000 win32u.dll
7FF81B100000 GDI32.dll
7FF818AC0000 gdi32full.dll
7FF818900000 msvcp_win.dll
7FF8189A0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF81AE80000 advapi32.dll
7FF81A080000 msvcrt.dll
7FF81B2F0000 sechost.dll
7FF819910000 RPCRT4.dll
7FF818140000 CRYPTBASE.DLL
7FF819190000 bcryptPrimitives.dll
7FF81B0C0000 IMM32.DLL
