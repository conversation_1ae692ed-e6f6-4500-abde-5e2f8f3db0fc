<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接调用量子资源API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .search-box {
            margin-bottom: 20px;
        }
        input {
            width: 300px;
            padding: 10px;
            font-size: 16px;
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
        }
        .results {
            margin-top: 20px;
        }
        .anime-item {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .anime-title {
            font-weight: bold;
            color: #333;
        }
        .anime-info {
            color: #666;
            margin-top: 5px;
        }
        .loading {
            text-align: center;
            color: #666;
        }
        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            color: green;
            background: #e6ffe6;
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>直接调用量子资源API测试</h1>
    
    <div class="search-box">
        <input type="text" id="searchInput" placeholder="输入动漫名称进行测试" />
        <button onclick="testDirectCall()">测试直接调用</button>
        <button onclick="testCorsCall()">测试CORS调用</button>
    </div>
    
    <div id="results" class="results"></div>

    <script>
        // 测试直接调用（和测试页面一样的方式）
        async function testDirectCall() {
            const keyword = document.getElementById('searchInput').value.trim();
            const resultsDiv = document.getElementById('results');
            
            if (!keyword) {
                alert('请输入搜索关键词');
                return;
            }
            
            resultsDiv.innerHTML = '<div class="loading">测试直接调用中...</div>';
            
            try {
                const apiUrl = `https://cj.lziapi.com/api.php/provide/vod/?ac=list&wd=${encodeURIComponent(keyword)}&pg=1`;
                console.log('直接调用URL:', apiUrl);
                
                const response = await fetch(apiUrl);
                const data = await response.json();
                
                console.log('直接调用响应:', data);
                
                if (data.code === 1) {
                    resultsDiv.innerHTML = `<div class="success">✅ 直接调用成功！找到 ${data.total} 个结果</div>`;
                    if (data.list && data.list.length > 0) {
                        displayResults(data.list, '直接调用结果');
                    }
                } else {
                    resultsDiv.innerHTML = '<div class="error">❌ 直接调用失败：API返回错误</div>';
                }
                
            } catch (error) {
                console.error('直接调用失败:', error);
                resultsDiv.innerHTML = `<div class="error">❌ 直接调用失败: ${error.message}</div>`;
            }
        }
        
        // 测试带CORS头的调用（模拟项目中的调用方式）
        async function testCorsCall() {
            const keyword = document.getElementById('searchInput').value.trim();
            const resultsDiv = document.getElementById('results');
            
            if (!keyword) {
                alert('请输入搜索关键词');
                return;
            }
            
            resultsDiv.innerHTML = '<div class="loading">测试CORS调用中...</div>';
            
            try {
                const apiUrl = `https://cj.lziapi.com/api.php/provide/vod/?ac=list&wd=${encodeURIComponent(keyword)}&pg=1`;
                console.log('CORS调用URL:', apiUrl);
                
                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json, text/plain, */*',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });
                
                const data = await response.json();
                console.log('CORS调用响应:', data);
                
                if (data.code === 1) {
                    resultsDiv.innerHTML = `<div class="success">✅ CORS调用成功！找到 ${data.total} 个结果</div>`;
                    if (data.list && data.list.length > 0) {
                        displayResults(data.list, 'CORS调用结果');
                    }
                } else {
                    resultsDiv.innerHTML = '<div class="error">❌ CORS调用失败：API返回错误</div>';
                }
                
            } catch (error) {
                console.error('CORS调用失败:', error);
                resultsDiv.innerHTML = `<div class="error">❌ CORS调用失败: ${error.message}</div>`;
            }
        }
        
        function displayResults(animeList, title) {
            const resultsDiv = document.getElementById('results');
            
            let html = resultsDiv.innerHTML + `<h3>${title}：</h3>`;
            
            animeList.slice(0, 3).forEach(anime => {
                html += `
                    <div class="anime-item">
                        <div class="anime-title">${anime.vod_name}</div>
                        <div class="anime-info">
                            年份: ${anime.vod_year || '未知'} | 
                            评分: ${anime.vod_score || '暂无'} | 
                            状态: ${anime.vod_remarks || '未知'}
                        </div>
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
        }
        
        // 回车键搜索
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testDirectCall();
            }
        });
    </script>
</body>
</html>
