# 动漫网站数据库设计

## 数据库表结构设计

### 1. 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(255) COMMENT '头像URL',
    role ENUM('user', 'admin') DEFAULT 'user' COMMENT '用户角色',
    status ENUM('active', 'banned', 'inactive') DEFAULT 'active' COMMENT '用户状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);
```

### 2. 动漫分类表 (categories)
```sql
CREATE TABLE categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);
```

### 3. 动漫表 (anime)
```sql
CREATE TABLE anime (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '动漫ID',
    title VARCHAR(200) NOT NULL COMMENT '动漫标题',
    original_title VARCHAR(200) COMMENT '原始标题',
    description TEXT COMMENT '动漫描述',
    poster VARCHAR(255) COMMENT '海报图片URL',
    banner VARCHAR(255) COMMENT '横幅图片URL',
    year INT COMMENT '年份',
    season ENUM('spring', 'summer', 'autumn', 'winter') COMMENT '季节',
    status ENUM('ongoing', 'completed', 'upcoming') DEFAULT 'upcoming' COMMENT '状态',
    total_episodes INT DEFAULT 0 COMMENT '总集数',
    current_episodes INT DEFAULT 0 COMMENT '当前集数',
    rating DECIMAL(3,1) DEFAULT 0.0 COMMENT '评分',
    rating_count INT DEFAULT 0 COMMENT '评分人数',
    view_count BIGINT DEFAULT 0 COMMENT '观看次数',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);
```

### 4. 动漫分类关联表 (anime_categories)
```sql
CREATE TABLE anime_categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    anime_id BIGINT NOT NULL COMMENT '动漫ID',
    category_id BIGINT NOT NULL COMMENT '分类ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (anime_id) REFERENCES anime(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
    UNIQUE KEY uk_anime_category (anime_id, category_id)
);
```

### 5. 剧集表 (episodes)
```sql
CREATE TABLE episodes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '剧集ID',
    anime_id BIGINT NOT NULL COMMENT '动漫ID',
    episode_number INT NOT NULL COMMENT '集数',
    title VARCHAR(200) COMMENT '剧集标题',
    description TEXT COMMENT '剧集描述',
    duration INT COMMENT '时长（秒）',
    video_url VARCHAR(500) COMMENT '视频URL',
    thumbnail VARCHAR(255) COMMENT '缩略图URL',
    view_count BIGINT DEFAULT 0 COMMENT '观看次数',
    is_published BOOLEAN DEFAULT FALSE COMMENT '是否发布',
    published_at TIMESTAMP NULL COMMENT '发布时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (anime_id) REFERENCES anime(id) ON DELETE CASCADE,
    UNIQUE KEY uk_anime_episode (anime_id, episode_number)
);
```

### 6. 用户收藏表 (user_favorites)
```sql
CREATE TABLE user_favorites (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    anime_id BIGINT NOT NULL COMMENT '动漫ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (anime_id) REFERENCES anime(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_anime (user_id, anime_id)
);
```

### 7. 观看历史表 (watch_history)
```sql
CREATE TABLE watch_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    anime_id BIGINT NOT NULL COMMENT '动漫ID',
    episode_id BIGINT NOT NULL COMMENT '剧集ID',
    progress INT DEFAULT 0 COMMENT '观看进度（秒）',
    last_watched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后观看时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (anime_id) REFERENCES anime(id) ON DELETE CASCADE,
    FOREIGN KEY (episode_id) REFERENCES episodes(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_episode (user_id, episode_id)
);
```

### 8. 评分表 (ratings)
```sql
CREATE TABLE ratings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    anime_id BIGINT NOT NULL COMMENT '动漫ID',
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 10) COMMENT '评分(1-10)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (anime_id) REFERENCES anime(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_anime_rating (user_id, anime_id)
);
```

### 9. 评论表 (comments)
```sql
CREATE TABLE comments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '评论ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    anime_id BIGINT NOT NULL COMMENT '动漫ID',
    episode_id BIGINT NULL COMMENT '剧集ID（可选）',
    parent_id BIGINT NULL COMMENT '父评论ID（回复）',
    content TEXT NOT NULL COMMENT '评论内容',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (anime_id) REFERENCES anime(id) ON DELETE CASCADE,
    FOREIGN KEY (episode_id) REFERENCES episodes(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE
);
```

## 索引设计

```sql
-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);

-- 动漫表索引
CREATE INDEX idx_anime_status ON anime(status);
CREATE INDEX idx_anime_year ON anime(year);
CREATE INDEX idx_anime_rating ON anime(rating DESC);
CREATE INDEX idx_anime_view_count ON anime(view_count DESC);
CREATE INDEX idx_anime_featured ON anime(is_featured);
CREATE INDEX idx_anime_created_at ON anime(created_at DESC);

-- 剧集表索引
CREATE INDEX idx_episodes_anime_id ON episodes(anime_id);
CREATE INDEX idx_episodes_published ON episodes(is_published, published_at);

-- 收藏表索引
CREATE INDEX idx_favorites_user_id ON user_favorites(user_id);
CREATE INDEX idx_favorites_anime_id ON user_favorites(anime_id);

-- 观看历史索引
CREATE INDEX idx_watch_history_user_id ON watch_history(user_id);
CREATE INDEX idx_watch_history_anime_id ON watch_history(anime_id);
CREATE INDEX idx_watch_history_last_watched ON watch_history(last_watched_at DESC);

-- 评论表索引
CREATE INDEX idx_comments_anime_id ON comments(anime_id);
CREATE INDEX idx_comments_episode_id ON comments(episode_id);
CREATE INDEX idx_comments_user_id ON comments(user_id);
CREATE INDEX idx_comments_parent_id ON comments(parent_id);
CREATE INDEX idx_comments_created_at ON comments(created_at DESC);
```
