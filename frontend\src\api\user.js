// 用户相关API - 处理用户登录、注册、个人信息等接口
import api from './index'

// 用户登录
export const login = (credentials) => {
  return api.post('/auth/login', credentials)
}

// 用户注册
export const register = (userData) => {
  return api.post('/auth/register', userData)
}

// 用户登出
export const logout = () => {
  return api.post('/auth/logout')
}

// 获取用户信息
export const getUserInfo = () => {
  return api.get('/user/profile')
}

// 更新用户信息
export const updateUserInfo = (userData) => {
  return api.put('/user/profile', userData)
}

// 修改密码
export const changePassword = (passwordData) => {
  return api.put('/user/password', passwordData)
}

// 获取用户收藏列表
export const getUserFavorites = (page = 1, size = 20) => {
  return api.get('/user/favorites', { params: { page, size } })
}

// 添加收藏
export const addFavorite = (animeId) => {
  return api.post('/user/favorites', { animeId })
}

// 取消收藏
export const removeFavorite = (animeId) => {
  return api.delete(`/user/favorites/${animeId}`)
}

// 获取观看历史
export const getWatchHistory = (page = 1, size = 20) => {
  return api.get('/user/history', { params: { page, size } })
}

// 添加观看历史
export const addWatchHistory = (animeId, episodeId, progress) => {
  return api.post('/user/history', { animeId, episodeId, progress })
}
