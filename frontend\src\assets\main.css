@import './base.css';

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: var(--theme-background);
}

#app {
  min-height: 100vh;
  width: 100%;
  margin: 0;
  padding: 0;
  font-weight: normal;
}

/* Element Plus 轮播图样式修复 */
.el-carousel__container {
  height: 500px !important;
}

.el-carousel__item {
  height: 500px !important;
}

/* 轮播图指示器样式 */
.el-carousel__indicators--outside {
  margin-top: 20px;
}

.el-carousel__indicator {
  padding: 8px 4px;
}

.el-carousel__button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  border: 2px solid transparent;
}

.el-carousel__indicator.is-active .el-carousel__button {
  background-color: var(--theme-primary);
}

/* 链接样式 */
a {
  text-decoration: none;
  color: var(--theme-primary);
  transition: color 0.3s ease;
}

a:hover {
  color: var(--theme-primary-dark);
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
  width: 0px;
  height: 0px;
  background: transparent;
}

/* 对于Firefox浏览器隐藏滚动条 */
html {
  scrollbar-width: none;
}

/* 对于IE和Edge隐藏滚动条 */
body {
  -ms-overflow-style: none;
}

/* Element Plus Loading 遮罩主题样式 - 半透明版本 */
.el-loading-mask {
  background-color: rgba(var(--theme-background-rgb), 0.55) !important;
  backdrop-filter: blur(3px) !important;
  z-index: 999 !important; /* 低于导航栏的1000 */
}

.el-loading-text {
  color: var(--theme-text-primary) !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
}

.el-loading-spinner {
  color: var(--theme-primary) !important;
}

.el-loading-spinner .circular {
  color: var(--theme-primary) !important;
}

.el-loading-spinner .path {
  stroke: var(--theme-primary) !important;
}

/* Element Plus 分页器全局主题样式 */
.el-pagination {
  --el-pagination-bg-color: var(--theme-background);
  --el-pagination-text-color: var(--theme-text-primary);
  --el-pagination-border-radius: 6px;
  --el-pagination-button-color: var(--theme-text-secondary);
  --el-pagination-hover-color: var(--theme-primary);
  --el-pagination-button-bg-color: var(--theme-background);
  --el-pagination-button-disabled-color: var(--theme-text-placeholder);
  --el-pagination-button-disabled-bg-color: var(--theme-background-mute);
  --el-pagination-hover-bg-color: var(--theme-background-soft);
}

/* 强制覆盖分页器跳转输入框样式 */
.el-pagination__editor.el-input .el-input__wrapper {
  background: var(--theme-background) !important;
  border: 1px solid var(--theme-border) !important;
  box-shadow: none !important;
}

.el-pagination__editor.el-input .el-input__inner {
  color: var(--theme-text-primary) !important;
  background: transparent !important;
}

/* 分页器文本样式 */
.el-pagination__jump {
  color: var(--theme-text-primary) !important;
}

.el-pagination__total {
  color: var(--theme-text-secondary) !important;
}
