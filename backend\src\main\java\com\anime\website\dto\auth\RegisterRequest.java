package com.anime.website.dto.auth;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 注册请求DTO - 用户注册时提交的数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RegisterRequest {

    // 用户名
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度必须在3-20个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    private String username;

    // 邮箱
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;

    // 密码
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 50, message = "密码长度必须在6-50个字符之间")
    private String password;

    // 确认密码
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;

    // 昵称（可选）
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;

    /**
     * 验证密码是否一致
     */
    public boolean isPasswordMatching() {
        return password != null && password.equals(confirmPassword);
    }
}
