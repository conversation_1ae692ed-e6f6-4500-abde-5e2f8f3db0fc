-- 动漫网站数据库初始化脚本
-- 请在MySQL中执行此脚本来创建数据库和基础数据

-- 创建数据库
CREATE DATABASE IF NOT EXISTS anime_website 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE anime_website;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
    nickname VARCHAR(50) COMMENT '昵称',
    avatar VARCHAR(255) COMMENT '头像URL',
    role ENUM('USER', 'ADMIN') DEFAULT 'USER' COMMENT '用户角色',
    status ENUM('ACTIVE', 'BANNED', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '用户状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 创建动漫分类表
CREATE TABLE IF NOT EXISTS categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='动漫分类表';

-- 创建动漫表
CREATE TABLE IF NOT EXISTS anime (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '动漫ID',
    title VARCHAR(200) NOT NULL COMMENT '动漫标题',
    original_title VARCHAR(200) COMMENT '原始标题',
    description TEXT COMMENT '动漫描述',
    poster VARCHAR(255) COMMENT '海报图片URL',
    banner VARCHAR(255) COMMENT '横幅图片URL',
    year INT COMMENT '年份',
    season ENUM('SPRING', 'SUMMER', 'AUTUMN', 'WINTER') COMMENT '季节',
    status ENUM('ONGOING', 'COMPLETED', 'UPCOMING') DEFAULT 'UPCOMING' COMMENT '状态',
    total_episodes INT DEFAULT 0 COMMENT '总集数',
    current_episodes INT DEFAULT 0 COMMENT '当前集数',
    rating DECIMAL(3,1) DEFAULT 0.0 COMMENT '评分',
    rating_count INT DEFAULT 0 COMMENT '评分人数',
    view_count BIGINT DEFAULT 0 COMMENT '观看次数',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='动漫表';

-- 创建动漫分类关联表
CREATE TABLE IF NOT EXISTS anime_categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    anime_id BIGINT NOT NULL COMMENT '动漫ID',
    category_id BIGINT NOT NULL COMMENT '分类ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (anime_id) REFERENCES anime(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
    UNIQUE KEY uk_anime_category (anime_id, category_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='动漫分类关联表';

-- 创建剧集表
CREATE TABLE IF NOT EXISTS episodes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '剧集ID',
    anime_id BIGINT NOT NULL COMMENT '动漫ID',
    episode_number INT NOT NULL COMMENT '集数',
    title VARCHAR(200) COMMENT '剧集标题',
    description TEXT COMMENT '剧集描述',
    duration INT COMMENT '时长（秒）',
    video_url VARCHAR(500) COMMENT '视频URL',
    thumbnail VARCHAR(255) COMMENT '缩略图URL',
    view_count BIGINT DEFAULT 0 COMMENT '观看次数',
    is_published BOOLEAN DEFAULT FALSE COMMENT '是否发布',
    published_at TIMESTAMP NULL COMMENT '发布时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (anime_id) REFERENCES anime(id) ON DELETE CASCADE,
    UNIQUE KEY uk_anime_episode (anime_id, episode_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='剧集表';

-- 插入基础分类数据
INSERT INTO categories (name, description, sort_order) VALUES
('动作', '充满战斗和冒险的动漫', 1),
('冒险', '探索未知世界的故事', 2),
('喜剧', '轻松幽默的动漫', 3),
('剧情', '注重故事情节的动漫', 4),
('奇幻', '魔法和超自然元素的动漫', 5),
('恋爱', '以爱情为主题的动漫', 6),
('科幻', '科学幻想题材的动漫', 7),
('悬疑', '推理和悬疑题材的动漫', 8),
('校园', '学校生活为背景的动漫', 9),
('运动', '体育竞技类动漫', 10);

-- 插入测试管理员用户（密码：admin123，已加密）
INSERT INTO users (username, email, password, nickname, role, status) VALUES
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9P8jYKqVPtB8RGy', '管理员', 'ADMIN', 'ACTIVE');

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);

CREATE INDEX idx_anime_status ON anime(status);
CREATE INDEX idx_anime_year ON anime(year);
CREATE INDEX idx_anime_rating ON anime(rating DESC);
CREATE INDEX idx_anime_view_count ON anime(view_count DESC);
CREATE INDEX idx_anime_featured ON anime(is_featured);
CREATE INDEX idx_anime_created_at ON anime(created_at DESC);

CREATE INDEX idx_episodes_anime_id ON episodes(anime_id);
CREATE INDEX idx_episodes_published ON episodes(is_published, published_at);

COMMIT;
