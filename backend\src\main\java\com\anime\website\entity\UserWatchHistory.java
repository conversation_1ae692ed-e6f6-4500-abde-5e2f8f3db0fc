package com.anime.website.entity;

import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户观看历史实体类 - 存储用户的观看记录和进度
 */
@Entity
@Table(name = "user_watch_history", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"user_id", "episode_id"})
})
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UserWatchHistory extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "anime_id", nullable = false)
    private Anime anime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "episode_id")
    private Episode episode;

    @Column(name = "watch_progress")
    private Integer watchProgress = 0; // 观看进度（秒）

    @Column(name = "last_watch_time")
    private LocalDateTime lastWatchTime; // 最后观看时间
}
